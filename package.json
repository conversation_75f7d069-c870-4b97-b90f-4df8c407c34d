{"name": "web-app", "version": "0.0.4", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.8.3", "dingtalk-jsapi": "^3.0.46", "moment": "^2.30.1", "qs": "^6.14.0", "vant": "^4.9.18", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "postcss": "^8.1.0", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.56.1", "vconsole": "^3.15.1", "vite": "5.4.11", "vite-plugin-vue-devtools": "^7.7.2"}}