import {fileURLToPath, URL} from 'node:url'

import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig(({mode, command}) => {
    const env = loadEnv(mode, process.cwd());
    const {VITE_APP_BASE_FILE} = env;
    return {
        base: VITE_APP_BASE_FILE,
        plugins: [
            vue()
        ],
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            },
        },
        server: {
            port: 81,
            host: true,
            open: true,
            proxy: {
                '/dev-api': {
                    target: 'http://*************:8089/ncras',
                    changeOrigin: true,
                    rewrite: p => p.replace(/^\/dev-api/, ''),
                }
            }
        },
        css: {
            preprocessorOptions: {
                scss: {
                    // additionalData: `@import "@/styles/variables.scss";` // 如果有全局变量
                }
            }
        }
    }
})
