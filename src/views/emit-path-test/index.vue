<template>
  <div class="emit-path-test">
    <div class="test-header">
      <h2>EmitPath 功能测试</h2>
    </div>

    <div class="test-section">
      <h3>默认模式（emitPath: false）</h3>
      <div class="test-item">
        <multi-level-tree-select
          v-model="selectedValues1"
          :items="testData"
          :emit-path="false"
          :multiple="true"
          @change="handleChange1"
        />
      </div>
      <div class="result">
        <p><strong>返回值类型：</strong>叶子节点value数组</p>
        <p><strong>选中的值：</strong>{{ JSON.stringify(selectedValues1, null, 2) }}</p>
      </div>
    </div>

    <div class="test-section">
      <h3>路径模式（emitPath: true）</h3>
      <div class="test-item">
        <multi-level-tree-select
          v-model="selectedValues2"
          :items="testData"
          :emit-path="true"
          :multiple="true"
          @change="handleChange2"
        />
      </div>
      <div class="result">
        <p><strong>返回值类型：</strong>路径数组的数组</p>
        <p><strong>选中的路径：</strong></p>
        <pre>{{ JSON.stringify(selectedValues2, null, 2) }}</pre>
        <div v-if="selectedValues2.length > 0">
          <p><strong>路径解析：</strong></p>
          <ul>
            <li v-for="(path, index) in selectedValues2" :key="index">
              路径{{ index + 1 }}: {{ getPathText(path) }}
            </li>
          </ul>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>预设路径测试</h3>
      <div class="test-buttons">
        <button @click="setDefaultPaths">设置默认路径</button>
        <button @click="clearPaths">清空选择</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MultiLevelTreeSelect from '@/components/multi-level-tree-select/index.vue'

defineOptions({
  name: 'EmitPathTest'
})

const selectedValues1 = ref([])
const selectedValues2 = ref([])

// 测试数据
const testData = ref([
  {
    text: '广东省',
    value: 'guangdong',
    children: [
      {
        text: '广州市',
        value: 'guangzhou',
        children: [
          {
            text: '天河区',
            value: 'tianhe',
            children: [
              { text: '珠江新城街道', value: 'zhujiangxincheng' },
              { text: '石牌街道', value: 'shipai' }
            ]
          },
          {
            text: '越秀区',
            value: 'yuexiu',
            children: [
              { text: '北京街道', value: 'beijing-street' },
              { text: '六榕街道', value: 'liurong' }
            ]
          }
        ]
      },
      {
        text: '深圳市',
        value: 'shenzhen',
        children: [
          {
            text: '南山区',
            value: 'nanshan',
            children: [
              { text: '南头街道', value: 'nantou' },
              { text: '沙河街道', value: 'shahe' }
            ]
          }
        ]
      }
    ]
  },
  {
    text: '北京市',
    value: 'beijing',
    children: [
      {
        text: '北京市',
        value: 'beijing-city',
        children: [
          {
            text: '朝阳区',
            value: 'chaoyang',
            children: [
              { text: '三里屯街道', value: 'sanlitun' },
              { text: '建外街道', value: 'jianwai' }
            ]
          }
        ]
      }
    ]
  }
])

// 创建value到text的映射
const valueToTextMap = ref({})

// 初始化映射
const initValueToTextMap = () => {
  const map = {}
  const traverse = (nodes) => {
    nodes.forEach(node => {
      map[node.value] = node.text
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(testData.value)
  valueToTextMap.value = map
}

initValueToTextMap()

const handleChange1 = (values) => {
  console.log('默认模式选择变化:', values)
}

const handleChange2 = (paths) => {
  console.log('路径模式选择变化:', paths)
}

// 将路径转换为可读文本
const getPathText = (path) => {
  if (!Array.isArray(path)) return path
  return path.map(value => valueToTextMap.value[value] || value).join(' -> ')
}

// 设置默认路径
const setDefaultPaths = () => {
  selectedValues2.value = [
    ['guangdong', 'guangzhou', 'tianhe', 'zhujiangxincheng'],
    ['beijing', 'beijing-city', 'chaoyang', 'sanlitun']
  ]
}

// 清空选择
const clearPaths = () => {
  selectedValues1.value = []
  selectedValues2.value = []
}
</script>

<style lang="scss" scoped>
.emit-path-test {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      color: #323233;
      font-size: 24px;
      margin: 0;
    }
  }

  .test-section {
    margin-bottom: 40px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      color: #323233;
      font-size: 18px;
      margin: 0 0 20px 0;
      border-bottom: 2px solid #1989fa;
      padding-bottom: 10px;
    }

    .test-item {
      margin-bottom: 20px;
    }

    .result {
      padding: 15px;
      background: #f7f8fa;
      border-radius: 6px;
      border-left: 4px solid #1989fa;

      p {
        margin: 8px 0;
        color: #646566;
        font-size: 14px;
      }

      pre {
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        font-size: 12px;
        overflow-x: auto;
        margin: 10px 0;
      }

      ul {
        margin: 10px 0;
        padding-left: 20px;

        li {
          margin: 5px 0;
          color: #646566;
          font-size: 14px;
        }
      }
    }

    .test-buttons {
      display: flex;
      gap: 10px;

      button {
        padding: 8px 16px;
        background: #1989fa;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          background: #1976d2;
        }
      }
    }
  }
}
</style>
