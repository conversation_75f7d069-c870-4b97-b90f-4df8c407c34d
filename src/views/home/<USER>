<script setup>
import {ref, onActivated} from "vue";
import {useRouter} from "vue-router";
import {axiosGet} from "@/commonjs/axiosUtils";
import {getUserInfo} from "@/commonjs/accountUtils";
import WeekSlider from "@/components/weekSlider/index.vue";
import MultiLevelTreeSelect from "@/components/multi-level-tree-select/index.vue";
import moment from "moment";
import {useStore} from "vuex";

defineOptions({
  name: 'home'
})

const router = useRouter()
const defaultDate = ref(undefined)
const showRole = ref(false)
const list = ref([])
const store = useStore();
const userInfo = getUserInfo();
const classType = ref(null)
const wrapperRef = ref(null)
const finish = ref(false)
const loading = ref(false)
const pageIndex = ref(0)
const scrollTop = ref(0)
const classList = ref([])
const classListBack = ref([])
const spaceIds = ref([])
const spaceIdsBack = ref([])
const classTree = ref([])
const treeList = ref([])
const showSpaceSelector = ref(false)
const showClassTypePicker = ref(false)
const showClassSelector = ref(false)

const classTypeOps = [
  {text: '全部类型', value: null},
  {text: '教学班', value: 1},
  {text: '行政班', value: 0},
]

// 获取当前选中班级类型的文本
const getClassTypeText = () => {
  const selected = classTypeOps.find(item => item.value === classType.value)
  return selected ? selected.text : '全部班级'
}

// 处理班级类型选择
const handleClassTypeConfirm = ({selectedOptions}) => {
  classType.value = selectedOptions[0].value
  showClassTypePicker.value = false
  // 重新获取数据
  refreshList()
}

const date = ref(moment().format('YYYY-MM-DD'))

const roleMap = {
  0: {
    name: '老师',
    url: '/mobile/course/getTeacherCourseList'
  },
  1: {
    name: '管理员',
    url: '/mobile/course/getAllCourseListForPage'
  }
}


const handleScroll = (event) => {
  const target = event.target
  scrollTop.value = target.scrollTop
}

const currentRole = ref(store.state.currentRole || 0)

const roleChange = (val) => {
  currentRole.value = val
  store.commit('SET_CURRENT_ROLE', val)
  refreshList()
  showRole.value = false
}


const dateClickhandler = (row) => {
  date.value = row
  refreshList()
}

function itemClick(val) {
  router.push({
    name: 'detail',
    query: {
      date: date.value,
      bean: JSON.stringify(val)
    }
  })
}

function editPasswordClick() {
  router.push({
    name: 'editPassword'
  })
}


function refreshList() {
  pageIndex.value = 1
  finish.value = false
  loading.value = false
  getListData(pageIndex.value)
}

function loadMore() {
  if (loading.value) return
  if (finish.value) return
  pageIndex.value++
  getListData(pageIndex.value)
}

const getListData = async (pageIndex) => {
  loading.value = true
  if (currentRole.value === 1 && classTree.value.length === 0) {
    getClassTreeList()
    getTreeList()
  }
  const timeList = date.value.split('-').map(Number)

  let campusIds = []
  let gradeIds = []
  let classIds = []
  if (classList.value.length > 0) {
    classList.value.forEach(item => {
      if (item.length > 1) {
        campusIds.push(item[1].split('_')[1])
      }
      if (item.length === 4) {
        gradeIds.push(item[2].split('_')[1])
        classIds.push(item[3].split('_')[1])
      } else if (item.length === 3) {
        classIds.push(item[2].split('_')[1])
      }
    })
  }
  let {code, data} = await axiosGet(roleMap[currentRole.value].url, {
    year: timeList[0],
    pageSize: 20,
    classType: classType.value,
    pageIndex: pageIndex,
    campusIds: [...new Set(campusIds)].join(','),
    gradeIds: [...new Set(gradeIds)].join(','),
    classIds: [...new Set(classIds)].join(','),
    spaceIds: spaceIds.value.join(','),

    month: timeList[1],
    day: timeList[2],
    week: moment(date.value).day()
  })
  loading.value = false
  if (code === 200) {
    if (currentRole.value === 0) {
      finish.value = true
      list.value = data
    } else {
      finish.value = data.list.length < 20
      if (pageIndex === 1) {
        list.value = data.list
      } else {
        list.value = [...list.value, ...data.list]
      }
    }
  }
}

onActivated(() => {
  wrapperRef.value.scrollTop = scrollTop.value
})


async function getClassTreeList() {
  const {code, data} = await axiosGet('/manage/class/getClassTreeList')
  if (code === 200) {
    classTree.value = getLableData(data, 1)
  }
}

function getLableData(list, type = 0) {
  return list.map(item => {
    let children = undefined
    if (item.children && item.children.length > 0) {
      children = getLableData(item.children,type)
    }
    return {
      ...item,
      text: item.label,
      children: children,
      value: type === 0 ? item.value : (item.classType + '_' + item.value)
    }
  })
}

async function getTreeList() {
  const {code, data} = await axiosGet('/device/getCampusSpaceTreeList')
  if (code === 200) {
    treeList.value = getLableData(data)
  }
}

// 获取选中场地的显示文本
const getSelectedSpaceText = () => {
  if (spaceIds.value.length === 0) return '全部场地'
  return `已选${spaceIds.value.length}个场地`
}

// 获取选中学段的显示文本
const getSelectedClassText = () => {
  if (classList.value.length === 0) return '全部学段'
  return `已选${classList.value.length}个学段`
}
</script>

<template>
  <div class="wrapper" ref="wrapperRef" @scroll="handleScroll">
    <div class="top-role-wrapper" @click="showRole=true">
      <div class="role-name">{{ roleMap[currentRole].name }}</div>
      <van-icon name="arrow-down"/>
    </div>

    <van-list
        v-model="loading"
        :finished="finish"
        :offset="100"
        @load="loadMore"
        class="list-wrapper">
      <div class="user-info">
        <div class="name">{{ userInfo.userName }}</div>
        <div class="phone">{{ userInfo.mobile }}</div>
        <div class="password" @click="editPasswordClick">课堂密码</div>
      </div>

      <div class="desc-text">课程点名查看</div>

      <van-sticky offset-top="28">
        <week-slider
            @dateClick="dateClickhandler"
            :showYear="true"
            v-model:defaultDate="defaultDate"/>

        <!-- 筛选选项卡 -->
        <div class="filter-tabs" v-if="currentRole===1">
          <div class="filter-item" @click="showClassTypePicker = true">
            <span class="filter-text">{{ getClassTypeText() }}</span>
            <van-icon name="arrow-down" class="filter-arrow"/>
          </div>
          <div class="filter-item" @click="()=>{
            showClassSelector = true
          }">
            <span class="filter-text">{{ getSelectedClassText() }}</span>
            <van-icon name="arrow-down" class="filter-arrow"/>
          </div>
          <div class="filter-item" @click="()=>{
            showSpaceSelector = true
          }">
            <span class="filter-text">{{ getSelectedSpaceText() }}</span>
            <van-icon name="arrow-down" class="filter-arrow"/>
          </div>
        </div>

      </van-sticky>

      <div class="item"
           @click="itemClick(item)"
           v-for="(item,index) in list" :key="index">
        <div class="class-name">{{ item.className }}</div>
        <div class="second-text">学科：{{ item.subject }}</div>
        <div class="second-text">教学场地：{{ item.spaceName }}</div>
        <div class="second-text">上课节次：第{{ item.lesson }}节</div>
        <div class="second-text">节次时间：{{
            item.startTime ? moment(item.startTime).format('HH:mm') : ''
          }}-{{ item.endTime ? moment(item.endTime).format('HH:mm') : '' }}
        </div>
        <div class="second-text">
          学生人数：{{
            (item.enrolledStudents || 0) + (item.studentsPresent || 0) + (item.excusedAbsence || 0) + (item.truancy || 0)
          }}
        </div>
        <div class="data-static">
          <div class="data">
            应到人数：<span style="color:#0054AD">{{ item.enrolledStudents }}</span>
          </div>
          <div class="data">
            实到人数：<span style="color:#00E33D">{{ item.studentsPresent }}</span>
          </div>
          <div class="data">
            请假人数：<span style="color:#F0BD05">{{ item.excusedAbsence }}</span>
          </div>
          <div class="data">
            缺勤人数：<span style="color:#ED1202">{{ item.truancy }}</span>
          </div>
        </div>
        <div class="look-detail">
          查看详情
          <van-icon name="arrow"/>
        </div>
      </div>
    </van-list>
  </div>
  <!-- 场地选择弹窗 -->
  <van-popup v-model:show="showSpaceSelector" position="bottom" round style="height: 70vh">
    <div class="space-selector-wrapper">
      <div class="selector-header">
        <div class="header-title">选择场地</div>
        <van-button
            type="primary"
            size="small"
            @click="()=>{
              spaceIds = [...spaceIdsBack]
            showSpaceSelector=false
            refreshList()
            }">
          确定
        </van-button>
      </div>
      <multi-level-tree-select
          v-model="spaceIdsBack"
          :items="treeList"
      />
    </div>
  </van-popup>

  <!-- 学段选择弹窗 -->
  <van-popup v-model:show="showClassSelector" position="bottom" round style="height: 70vh">
    <div class="class-selector-wrapper">
      <div class="selector-header">
        <div class="header-title">选择学段</div>
        <van-button
            type="primary"
            size="small"
            @click="()=>{
              classList = [...classListBack]
              showClassSelector = false
              refreshList()
            }"
        >
          确定
        </van-button>
      </div>
      <multi-level-tree-select
          emit-path
          v-model="classListBack"
          :items="classTree"
      />
    </div>
  </van-popup>

  <!-- 班级类型选择弹窗 -->
  <van-popup v-model:show="showClassTypePicker" position="bottom" round>
    <van-picker
        :columns="classTypeOps"
        @confirm="handleClassTypeConfirm"
        @cancel="showClassTypePicker = false"
    />
  </van-popup>

  <!-- 角色选择弹窗 -->
  <van-popup v-model:show="showRole" position="bottom" round style="height: 400px">
    <div class="role-wrapper">
      <div class="role-title">选择角色</div>
      <div class="role-item"
           @click="roleChange(1)"
           v-if="userInfo.isManager === 1">管理员
      </div>
      <div class="role-item"
           @click="roleChange(0)"
      >教师
      </div>
      <div style="flex: 1"></div>
      <van-button type="primary" block v-if="false" class="">确认</van-button>

    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
.wrapper {
  width: 100%;
  height: 100%;
  background-color: #f9f9f9;


  .top-role-wrapper {
    display: flex;
    box-sizing: border-box;
    height: 32px;
    padding: 0 29px;
    align-items: center;
    color: var(--theme-color);

    .role-name {
      font-size: 13px;
      font-weight: 500;
      margin-right: 10px;
    }
  }

  .list-wrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 0 9px;
    height: calc(100% - 32px);
    overflow-y: auto;

    .user-info {
      width: 100%;
      display: flex;
      border-radius: 13px;
      box-sizing: border-box;
      padding: 0 14px 0 23px;
      background: #FFFFFF;
      align-items: center;
      height: 70px;

      .name {
        font-size: 18px;
        font-weight: 700;
        color: rgba(61, 61, 61, 1);
      }

      .phone {
        flex: 1;
        padding: 0 13px;
        font-size: 16px;
        font-weight: 400;
        color: #808080;
      }

      .password {
        color: var(--theme-color);
      }
    }

    .desc-text {
      font-size: 12px;
      height: 34px;
      box-sizing: border-box;
      padding-top: 6px;
      font-weight: 400;
      color: rgba(158, 157, 157, 1);
    }

    .item {
      margin-top: 8px;
      box-sizing: border-box;
      padding: 23px 18px 0;
      background-color: white;
      border-radius: 10px;

      .class-name {
        font-size: 18.35px;
        font-weight: 700;
        color: rgba(0, 0, 0, 100);
      }

      .second-text {
        margin-top: 10px;
        font-size: 16.06px;
        font-weight: 400;
        color: rgba(134, 150, 187, 1);
      }

      .data-static {
        display: flex;
        flex-wrap: wrap;

        .data {
          margin-top: 10px;
          font-size: 16.06px;
          font-weight: 400;
          color: rgba(134, 150, 187, 1);
          flex: 0 0 50%;
        }
      }

      .look-detail {
        width: 100%;
        height: 50px;
        margin-top: 6px;
        line-height: 50px;
        text-align: center;
        border-top: 1px solid #E5E5E5;
        color: var(--theme-color);
      }
    }
  }
}

.filter-tabs {
  display: flex;
  background: #fff;
  border-radius: 6px;
  padding: 1px 0;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .filter-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-right: 1px solid #ebedf0;
    min-width: 0; // 允许文本截断

    &:last-child {
      border-right: none;
    }

    &:active {
      background-color: #f7f8fa;
    }

    .filter-text {
      font-size: 13px;
      color: #323233;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
    }

    .filter-arrow {
      font-size: 12px;
      color: #969799;
      margin-left: 6px;
      flex-shrink: 0;
    }
  }
}

.class-selector-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
    background: #fff;

    .header-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
    }
  }
}

.space-selector-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
    background: #fff;

    .header-title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
    }
  }
}

.role-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  box-sizing: border-box;
  padding: 14px;
  background-color: #f9f9f9;

  .role-title {
    margin-bottom: 80px;
    font-size: 14.69px;
    font-weight: 400;
    letter-spacing: 0px;
    line-height: 14.93px;
    color: rgba(26, 26, 26, 1);
    text-align: center;
    vertical-align: top;
  }

  .role-item {
    margin: 10px;
    width: 240px;
    line-height: 40px;
    text-align: center;
    color: var(--theme-color);
    background-color: rgba(var(--theme-color-rgb), 0.15);
  }

}
</style>
