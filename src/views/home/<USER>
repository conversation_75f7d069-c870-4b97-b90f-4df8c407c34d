<script setup>

import {axiosGet, axiosPost} from "@/commonjs/axiosUtils";
import {ref} from "vue";
import {useRoute} from "vue-router";
import {showSuccessToast} from "vant";
import moment from "moment/moment";

const route = useRoute();
const bean = ref(JSON.parse(route.query.bean))
const date = ref(route.query.date)
const showPop = ref(false)
const staticData = ref({})
const list = ref([])
const currentSelectUser = ref({})

const statusMap = {
  1: '实到',
  2: '请假',
  3: '缺勤'
}

async function getDetail() {
  const timeList = date.value.split('-').map(Number)
  const {code, data} = await axiosGet('/mobile/course/getClassCourseStudentDataForDay', {
    scheduleType: bean.value.scheduleType,
    classId: bean.value.classId,
    lesson: bean.value.lesson,
    year: timeList[0],
    month: timeList[1],
    day: timeList[2],
    week: moment(date.value).day(),
  })
  if (code === 200) {
    staticData.value = data
    list.value = data.list
  }
}

const statusShowPop = (item) => {
  if (item.status === 3) {
    currentSelectUser.value = item
    showPop.value = true
  }
}

async function changeStatus(status) {
  const timeList = date.value.split('-').map(Number)

  const {code} = await axiosPost('/mobile/course/setStudentAttendenceStatus', null,{
    status: status,
    studentId: currentSelectUser.value.studentId,
    scheduleType: bean.value.scheduleType,
    classId: bean.value.classId,
    lesson: bean.value.lesson,
    year: timeList[0],
    month: timeList[1],
    day: timeList[2],
    week: moment(date.value).day()
  })
  if (code === 200) {
    showPop.value = false
    showSuccessToast('设置成功')
    getDetail()
  }
}

function zwText(val) {
  if (val.row === 0 && val.column === 0) {
    return ''
  } else if (val.row === 0 && val.column === 1) {
    return '讲台左侧'
  } else if (val.row === 0 && val.column === 8) {
    return '讲台右侧'
  }
  return `${val.row}行${val.column}列`
}

getDetail()
</script>

<template>
  <div class="wrapper-data">
    <div class="content">
      <div class="class-name">{{ bean.className }}</div>
      <div class="static-data">
        <div class="static-item">
          <div class="data" style="color: #0054AD">{{ staticData.enrolledStudents }}</div>
          <div class="text">应到</div>
        </div>

        <div class="static-item">
          <div class="data" style="color: #00E33D">{{ staticData.studentsPresent }}</div>
          <div class="text">实到</div>
        </div>

        <div class="static-item">
          <div class="data" style="color: #F0BD05">{{ staticData.excusedAbsence }}</div>
          <div class="text">请假</div>
        </div>

        <div class="static-item">
          <div class="data" style="color: #F24D41">{{ staticData.truancy }}</div>
          <div class="text">缺勤</div>
        </div>
      </div>
      <div class="table-header">
        <div class="header-item">
          <div class="label">
            姓名
          </div>
        </div>

        <div class="header-item">
          <div class="label">
            座位
          </div>
        </div>

        <div class="header-item">
          <div class="label">
            点名
          </div>
        </div>
      </div>

      <div class="item-data" v-for="(item,index) in list" :key="index">
        <div class="name">{{ item.studentName }}</div>
        <div class="zw">{{ zwText(item) }}</div>
        <div class="status"
             :class="{'status-red':item.status===3,
             'status-yellow':item.status===2}"
             @click="statusShowPop(item)">{{ statusMap[item.status] }}</div>
      </div>
    </div>
  </div>

  <van-popup position="center"
             style="width: 80%"
             round
             v-model:show="showPop">
    <div class="pop-wrapper">
      <div class="name-po">{{ currentSelectUser.name }}</div>
      <div class="tips">手动修改点名状态，优先级将高于人脸识别记录</div>
      <div class="status-data" style="color: #20A142" @click="changeStatus(1)">实到</div>
      <div class="status-data" @click="changeStatus(2)">请假</div>
    </div>
  </van-popup>

</template>

<style scoped lang="scss">
.wrapper-data {
  padding: 12px 9px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #f9f9f9;

  .content {
    width: 100%;
    overflow-y: auto;
    height: 100%;
    background-color: white;
    border-top: 6px;
    box-sizing: border-box;
    padding: 20px 30px;

    .class-name {
      font-size: 16px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 22.4px;
      color: rgba(26, 26, 26, 1);
      text-align: left;
      vertical-align: top;
    }

    .static-data {
      width: 100%;
      display: flex;
      padding: 10px 0 20px;
      justify-content: space-around;
      align-items: center;

      .static-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 10px;
        justify-content: center;

        .data {
          font-size: 20px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 19.6px;
          color: rgba(0, 84, 173, 1);
          text-align: center;
          vertical-align: top;
        }

        .text {
          margin-top: 10px;
          font-size: 12px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 16.8px;
          color: rgba(162, 160, 160, 1);
          text-align: center;
          vertical-align: top;

        }
      }
    }

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;
      margin-bottom: 10px;
      font-weight: 400;
      letter-spacing: 0;
      line-height: 22.4px;
      color: rgba(118, 118, 118, 1);
      text-align: left;
      vertical-align: top;
    }

    .item-data {
      display: flex;
      padding: 9px 0;
      position: relative;
      align-items: center;

      .name {
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        color: rgba(61, 61, 61, 1);
      }

      .zw {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        font-weight: 400;
        color: rgba(61, 61, 61, 1);
      }

      .status {
        position: absolute;
        width: 51px;
        height: 23px;
        right: -14px;
        line-height: 23px;
        border-radius: 65.99px;
        background: #D1E8D7;
        color: #20A142;
        font-size: 12px;
        text-align: center;
      }

      .status-red {
        background: #FBACA3;
        color: #F24D41;
      }

      .status-yellow {
        background: #FFCF8B;
        color: #FF7D00;
      }
    }
  }
}

.pop-wrapper {
  display: flex;
  box-sizing: border-box;
  padding-top: 20px;
  height: 200px;
  overflow: hidden;
  width: 100%;
  flex-direction: column;

  .name-po {
    font-size: 17px;
    font-weight: 500;
    width: 100%;
    box-sizing: border-box;
    color: rgba(29, 33, 41, 1);
    text-align: center;
  }

  .tips {
    padding: 0 24px;
    flex: 1;
    box-sizing: border-box;
    font-size: 15px;
    color: rgba(78, 89, 105, 1);
    text-align: center;
  }

  .status-data {
    width: 100%;
    text-align: center;
    height: 44px;
    line-height: 44px;
    border-top: 1px solid #ededed;
    font-size: 16px;
    color: rgba(255, 125, 0, 1);
  }
}
</style>