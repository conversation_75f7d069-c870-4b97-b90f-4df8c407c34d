<script setup>
import WrapperData from "@/components/wrapper-data/index.vue";
import {reactive, ref} from "vue";
import LoadingPop from "@/components/loading-pop.vue";
import {axiosPost} from "@/commonjs/axiosUtils";
import {showFailToast, showToast} from "vant";
import {useRouter} from "vue-router";

const router = useRouter()
const show = ref(false)

const formRef = ref();

const form = reactive({
  password: '',
  password2: ''
})

const onSubmit = async () => {
  if (form.password !== form.password2) {
    showFailToast('两次密码输入不一致，请修改')
    return
  }
  show.value = true
  let {code} = await axiosPost('/manage/user/modifyPwd', {
    newPwd: form.password
  })
  show.value = false
  if (code === 200) {
    showToast('修改成功')
    router.back()
  }
}

</script>

<template>
  <wrapper-data>
    <van-form ref="formRef" @submit="onSubmit" class="form-data" scroll-to-error>
      <input-item v-model="form.password" label="输入密码" required type="password"/>
      <input-item v-model="form.password2" label="再次输入密码" required type="password"/>
      <van-button class="login" native-type="submit">保存</van-button>
    </van-form>
    <loading-pop :show="show"/>
  </wrapper-data>
</template>

<style lang="scss" scoped>
.form-data {
  box-sizing: border-box;
  padding: 14px 20px;
  width: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;

}

.login-field {
  height: 40px;
  font-size: 13px;
  padding: 0;

  .van-field__body {
    height: 100%;

    .van-field__control {
      height: 100%;
    }
  }
}

.login {
  width: calc(100% - 30px);
  margin-top: 40px;
  height: 50px;
  color: white;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 13px;
  background: var(--theme-color);
}
</style>