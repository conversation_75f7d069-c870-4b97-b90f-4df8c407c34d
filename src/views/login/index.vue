<template>
  <div>
  </div>
</template>

<script setup>
import {useRoute, useRouter} from 'vue-router'
import * as dd from "dingtalk-jsapi";
import {getCorpId, resetData, setToken, setUserInfo} from "@/commonjs/accountUtils";
import {axiosGet} from "@/commonjs/axiosUtils";
import {showLoadingToast} from "vant";
import {getVal} from "@/commonjs/paramsUtils";

const router = useRouter()
const route = useRoute()
const redirect = route.query.redirect
resetData()
showLoadingToast({
  message: '登录中...',
  duration: 1000
});

const getAuthCode = () => {
  let corpIdData = getCorpId()
  if (!corpIdData && process.env.NODE_ENV === 'development') {
    corpIdData = 'ding5089aef11df28b36f2c783f7214b6d69'
  }
  const code = getVal('code')
  if (!!code) {
    signIn(corpIdData, code)
    return;
  }
  if (!corpIdData) return
  dd.ready(() => {
    dd.runtime.permission.requestAuthCode({
      corpId: corpIdData,
      onSuccess: (info) => {
        signIn(corpIdData, info.code);
      },
      onFail: (err) => {
        console.log(err);
      }
    })
  })
}

const signIn = async (corpIdData, codeData) => {
  let {code, data} = await axiosGet('/userLogin/microApplicationNoLogin', {
    code: codeData,
    corpId: corpIdData,
  })
  if (code !== 200) return
  setToken(data.token)
  setUserInfo(data.user)
  router.replace(redirect ? decodeURIComponent(redirect) : '/')
}
getAuthCode()
</script>