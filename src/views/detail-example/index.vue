<template>
  <div class="detail-page">
    <div class="header">
      <h2>详情页面示例</h2>
      <p>演示如何使用全局事件总线通知列表页刷新</p>
    </div>

    <div class="content">
      <div class="section">
        <h3>模拟数据修改操作</h3>
        <div class="button-group">
          <van-button 
            type="primary" 
            @click="updateAttendanceData"
            :loading="loading1">
            更新考勤数据
          </van-button>
          
          <van-button 
            type="success" 
            @click="updateCourseData"
            :loading="loading2">
            更新课程信息
          </van-button>
          
          <van-button 
            type="warning" 
            @click="refreshHomeList"
            :loading="loading3">
            直接刷新首页
          </van-button>
        </div>
      </div>

      <div class="section">
        <h3>事件发送记录</h3>
        <div class="event-log">
          <div 
            v-for="(log, index) in eventLogs" 
            :key="index"
            class="log-item">
            <span class="time">{{ log.time }}</span>
            <span class="event">{{ log.event }}</span>
            <span class="data">{{ log.data }}</span>
          </div>
          <div v-if="eventLogs.length === 0" class="no-logs">
            暂无事件记录
          </div>
        </div>
      </div>

      <div class="section">
        <h3>返回操作</h3>
        <van-button 
          type="default" 
          @click="goBack"
          block>
          返回首页
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useDataRefresh } from '@/composables/useEventBus'

defineOptions({
  name: 'DetailExample'
})

const router = useRouter()
const { refreshHomeList: triggerRefresh, updateAttendance, notifyDataChange } = useDataRefresh()

// 加载状态
const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)

// 事件日志
const eventLogs = ref([])

// 添加事件日志
const addEventLog = (event, data) => {
  eventLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    event,
    data: JSON.stringify(data)
  })
  
  // 最多保留10条记录
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

// 模拟更新考勤数据
const updateAttendanceData = async () => {
  loading1.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟更新成功
    const attendanceData = {
      classId: '123',
      studentId: '456',
      status: 'present',
      updateTime: new Date().toISOString()
    }
    
    // 发送考勤更新事件
    updateAttendance(attendanceData)
    addEventLog('考勤数据更新', attendanceData)
    
    showToast('考勤数据更新成功')
  } catch (error) {
    showToast('更新失败')
  } finally {
    loading1.value = false
  }
}

// 模拟更新课程数据
const updateCourseData = async () => {
  loading2.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 模拟更新成功
    const courseData = {
      type: 'course',
      courseId: '789',
      action: 'update',
      updateTime: new Date().toISOString()
    }
    
    // 发送通用数据变更事件
    notifyDataChange(courseData)
    addEventLog('课程数据变更', courseData)
    
    showToast('课程信息更新成功')
  } catch (error) {
    showToast('更新失败')
  } finally {
    loading2.value = false
  }
}

// 直接刷新首页列表
const refreshHomeList = async () => {
  loading3.value = true
  
  try {
    // 模拟一些操作
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const refreshData = {
      source: 'detail-page',
      reason: 'manual-refresh',
      timestamp: Date.now()
    }
    
    // 直接触发首页刷新
    triggerRefresh(refreshData)
    addEventLog('首页列表刷新', refreshData)
    
    showToast('已通知首页刷新')
  } catch (error) {
    showToast('操作失败')
  } finally {
    loading3.value = false
  }
}

// 返回首页
const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.detail-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h2 {
      color: #323233;
      font-size: 20px;
      margin: 0 0 10px 0;
    }

    p {
      color: #646566;
      font-size: 14px;
      margin: 0;
    }
  }

  .content {
    .section {
      margin-bottom: 20px;
      padding: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      h3 {
        color: #323233;
        font-size: 16px;
        margin: 0 0 15px 0;
        border-bottom: 2px solid #1989fa;
        padding-bottom: 8px;
      }

      .button-group {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .van-button {
          height: 44px;
        }
      }

      .event-log {
        max-height: 300px;
        overflow-y: auto;

        .log-item {
          display: flex;
          flex-direction: column;
          padding: 12px;
          margin-bottom: 8px;
          background: #f7f8fa;
          border-radius: 6px;
          border-left: 4px solid #1989fa;

          .time {
            font-size: 12px;
            color: #969799;
            margin-bottom: 4px;
          }

          .event {
            font-size: 14px;
            color: #323233;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .data {
            font-size: 12px;
            color: #646566;
            font-family: monospace;
            background: #fff;
            padding: 8px;
            border-radius: 4px;
            word-break: break-all;
          }
        }

        .no-logs {
          text-align: center;
          color: #969799;
          font-size: 14px;
          padding: 20px;
        }
      }
    }
  }
}
</style>
