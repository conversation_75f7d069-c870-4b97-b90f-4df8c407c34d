<template>
  <div class="title-wrapper">
    <img v-if="showBack" :src="backImage" alt="" class="back-image" @click="backClickListener">
    <div :style="{color:whiteBack?'#fff':'#272727',
        fontSize:titleSize+'px',
        fontWeight:titleBold?'bold':'normal'}" class="title">{{ titleRel }}
    </div>
    <div v-if="rightText" :style="{color:whiteBack?'#fff':'#333'}"
         class="right-title"
         @click="rightClickListener">{{ rightText }}
    </div>
    <img v-if="showRightImage"
         :src="backImage"
         alt=""
         class="right-icon"
         @click="rightClick">
  </div>
</template>

<script>
import backWhite from '/src/assets/back-white.png'
import backBlack from '/src/assets/back-black.png'

export default {
  name: "TitleWrapper",
  props: {
    showBack: Boolean,
    whiteBack: Boolean,
    title: String,
    titleSize: {
      type: Number,
      default: 18
    },
    titleBold: {
      type: Boolean,
      default: false
    },
    useRouteTitle: {
      type: Boolean,
      default: true
    },
    rightText: String,
    backClick: Function,
    rightClick: Function,
    showRightImage: Boolean
  },
  computed: {
    backImage() {
      return this.whiteBack ? backWhite : backBlack
    },
    titleRel() {
      return this.useRouteTitle ? this.$route.meta.title : this.title
    }
  },
  methods: {
    backClickListener() {
      if (this.backClick) {
        this.backClick()
      } else {
        this.$router.back()
      }
    },
    rightClickListener() {
      this.rightClick && this.rightClick()
    }
  },
}
</script>

<style lang="scss" scoped>
.title-wrapper {
  width: 100%;
  height: 88px;
  max-height: 88px;
  position: relative;

  .back-image {
    width: 88px;
    height: 88px;
    position: absolute;
    left: 0;
    top: 0;
    object-fit: cover;
    box-sizing: border-box;
    padding: 24px;
  }

  .title {
    margin: auto auto;
    height: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #272727;
    line-height: 88px;
    text-align: center;
  }

  .right-title {
    height: 45px;
    line-height: 45px;
    font-size: 19px;
    top: 0;
    position: absolute;
    right: 10px;
    text-align: center;
  }

  .right-icon {
    width: 45px;
    top: 50%;
    transform: translateY(-50%);
    box-sizing: border-box;
    padding: 10px;
    object-fit: fill;
    position: absolute;
    right: 10px;
  }
}
</style>