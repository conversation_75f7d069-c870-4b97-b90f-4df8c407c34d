<script setup>
import {computed, onMounted, ref, watch} from "vue";
import {axiosGet} from "@/commonjs/axiosUtils";

const emit = defineEmits(['update:modelValue', 'onChange']);
const valueText = ref(null)
const optionsCas = ref([])
const value = ref(null)
const showSelect = ref(false)
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: null
  },
  multiple: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'text'
  },
  isSelect: {
    type: Boolean,
    default: false
  },
  extraSelectParams: {
    type: Object,
    default: undefined
  },
  showAllLabels: {
    type: Boolean,
    default: true
  },
  isCascader: {
    type: Boolean,
    default: false
  },
  selectUrl: {
    type: String,
    default: null
  },
  selectRemoteNameKey: {
    type: String,
    default: null
  },
  selectRemoteValueKey: {
    type: String,
    default: null
  },
  options: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: ''
  },
  labelTwo: {
    type: String,
    default: ''
  },
  border: {
    type: Boolean,
    default: true
  },
  placeholder: {
    type: String,
    default: ''
  },
  labelDesc: {
    type: String,
    default: ''
  },
  firstLabel: {
    type: String,
    default: ''
  },
  disabledList: {
    type: Array,
    default: []
  },
  formatterFun: {
    type: Function,
    default: null
  },
  maxlength: {
    type: Number,
    default: 30
  },
  min: {
    type: Number,
    default: 0
  },
  required: {
    type: Boolean,
    default: false
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

value.value = props.modelValue

const casList = computed(() => {
  if (props.options.length > 0) {
    if (props.disabledList && props.disabledList.length > 0) {
      return props.options.filter(item => item.children && item.children.length > 0).map(item => {
        return {
          ...item,
          children: item.children.map(item2 => {
            return {
              ...item2,
              disabled: props.disabledList.includes(item2.value)
            }
          })
        }
      })
    }
    return props.options
  }
  return optionsCas.value
})

if (props.firstLabel) {
  valueText.value = props.firstLabel
}

watch(() => props.firstLabel, (newValue) => {
  valueText.value = newValue
})

watch(() => props.modelValue, (newValue) => {
  if (props.isSelect) {
    if (Array.isArray(newValue)) {
      value.value = newValue
    } else {
      value.value = [newValue]
    }
  } else {
    value.value = newValue
  }
  if (props.isSelect && newValue !== null) {
    if (props.multiple) {
      let text = ''
      let index = 0
      props.options.forEach(item => {
        if (item.value === value.value[0]) {
          text += item.text
          if (item.children && item.children.length > 0) {
            item.children.forEach(item2 => {
              if (item2.value === value.value[index + 1]) {
                text += ('-' + item2.text)
              }
            })
          }
        }
      })
      valueText.value = text
    } else {
      valueText.value = props.options.find(item => item.value === value.value[0]).text
    }
  }
})

watch(() => value.value, (newValue) => {
  if (!props.isSelect && !props.isCascader) {
    emit('update:modelValue', newValue)
  }
})

const labelTips = computed(() => {
  if (props.placeholder) {
    return props.placeholder
  }
  const text = (props.isSelect || props.isCascader) ? '选择' : '输入'
  return '请' + (props.label.includes(text) ? props.label : (text + props.label))
})

const showSelectFun = () => {
  if (!props.disabled)
    showSelect.value = true
}

const hideSelectFun = () => {
  showSelect.value = false
}

const confirmSelectFun = (res) => {
  showSelect.value = false
  if (props.isSelect) {
    if (props.multiple) {
      emit('update:modelValue', res.selectedValues);
    } else {
      emit('update:modelValue', res.selectedValues[0]);
    }
  } else if (props.isCascader) {
    emit('update:modelValue', res.value);
  }
  emit('onChange', res.selectedOptions, res.selectedValues)
  if (props.showAllLabels) {
    valueText.value = res.selectedOptions.map(item => item.text).join('-')
  } else {
    valueText.value = res.selectedOptions[res.selectedOptions.length - 1].text
  }
}

const onChange = async (val) => {

}

onMounted(() => {
  if (props.isSelect || props.isCascader) {
    value.value = []
  }
})

if (props.selectUrl) {
  getListData()
}

async function getListData() {
  const {code, result} = await axiosGet(props.selectUrl, {
    ...props.extraSelectParams
  })
  if (code === 200) {
    if (props.formatterFun) {
      optionsCas.value = props.formatterFun(result)
      return
    }
    optionsCas.value = props.formatterFun ? props.formatterFun(result) : result.map(item => {
      return {
        text: item[props.selectRemoteNameKey],
        value: item[props.selectRemoteValueKey]
      }
    })
  }
}
</script>

<template>
  <div :style="{borderBottom: border ? '1px solid #E5E5E5' : 'none'}" class="input-wrapper-data">
    <div :class="{'label-wrapper-disabled':disabled}" class="label-wrapper">
      <div class="label">{{ label }}{{ labelDesc }}
      </div>
      <div v-if="required" class="required-icon">*</div>
    </div>
    <slot/>
    <van-field
        v-if="!isSelect&&!isCascader&&!$slots.default"
        v-model="value"
        :border="false"
        :disabled="disabled"
        :maxlength="maxlength"
        :min="min"
        :placeholder="labelTips"
        :readonly="readOnly"
        :rules="[{ required: required, message: labelTips }]"
        :type="type"
        arrow-direction="down"
        center
        class="login-field"
        size="large"/>

    <van-field
        v-if="(isSelect||isCascader)&&!$slots.default"
        :border="false"
        :disabled="disabled"
        :model-value="valueText"
        :placeholder="labelTips"
        :readonly="true"
        :rules="[{ required: required, message: labelTips }]"
        :type="type"
        arrow-direction="down"
        center
        class="login-field"
        is-link
        size="large"
        @click="showSelectFun"/>

    <slot name="bottom"/>

  </div>
  <van-popup v-if="isSelect"
             teleport="body"
             v-model:show="showSelect"
             position="bottom" round>
    <van-picker
        v-model="value"
        :columns="options"
        :columns-height="300"
        :show-toolbar="true"
        @cancel="hideSelectFun"
        @confirm="confirmSelectFun"
    />
  </van-popup>
  <van-popup v-if="isCascader"
             teleport="body"
             v-model:show="showSelect"
             position="bottom" round>
    <van-cascader
        v-model="value"
        :options="casList"
        :title="labelTips"
        @change="onChange"
        @close="hideSelectFun"
        @finish="confirmSelectFun"
    />
  </van-popup>
</template>

<style lang="scss" scoped>
.input-wrapper-data {
  display: flex;
  padding: 14px 0;
  width: 100%;
  box-sizing: border-box;
  align-items: flex-start;
  flex-direction: column;
  border-bottom: 1px solid rgba(229, 229, 229, 1);

  .label-wrapper {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 400;
    color: rgba(17, 41, 80, 1);

    .required-icon {
      color: red;
      margin: 2px 4px 0;
    }
  }

  .label-wrapper-disabled {
    display: flex;
    align-items: center;
    font-size: 22.67px;
    font-weight: 400;
    color: #505970;
  }

  :deep(.van-cell__right-icon) {
    color: var(--theme-color);
  }

  .login-field {
    margin-top: -5px;
    font-size: 15px;
    padding: 0;
  }
}


</style>