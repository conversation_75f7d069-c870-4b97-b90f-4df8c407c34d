# MultiLevelTreeSelect 多级树形选择组件

一个支持多级树形结构的选择组件，支持多选、单选，并且能够自适应不同层级数量的显示方式。

## 特性

- ✅ 支持无限层级的树形结构
- ✅ 支持多选和单选模式
- ✅ 面包屑导航：顶部显示层级路径，支持快速跳转
- ✅ 与 Vant UI 风格保持一致
- ✅ 支持自定义层级标题

- ✅ 完整的事件回调支持
- ✅ **智能选择机制**：点击展开，checkbox选择
- ✅ **级联选择**：选中父级自动选中所有子级
- ✅ **级联取消**：取消子级自动取消父级
- ✅ **半选状态**：部分子级选中时父级显示半选状态

## 交互说明

### 交互机制
- **面包屑导航**：顶部显示当前层级路径，点击可快速跳转到任意层级
- **点击文本区域**：进入下一级，显示子项内容
- **点击checkbox**：选中/取消选中当前项及其所有子项
- **级联选择**：选中父级时，所有子级自动选中
- **级联取消**：取消任一子级时，其所有父级自动取消选中
- **半选状态**：当部分子级被选中时，父级显示半选状态

## 基础用法

```vue
<template>
  <multi-level-tree-select
    v-model="selectedValues"
    v-model:selectedItems="selectedItemsWithText"
    :items="treeData"
    :multiple="true"
    :level-titles="['省份', '城市', '区县', '街道']"
    @change="handleChange"
    @update:selectedItems="handleSelectedItemsChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import MultiLevelTreeSelect from '@/components/multi-level-tree-select/index.vue'

const selectedValues = ref([])
const selectedItemsWithText = ref([])

const treeData = ref([
  {
    text: '广东省',
    value: 'guangdong',
    children: [
      {
        text: '广州市',
        value: 'guangzhou',
        children: [
          {
            text: '天河区',
            value: 'tianhe',
            children: [
              { text: '珠江新城街道', value: 'zhujiangxincheng' }
            ]
          }
        ]
      }
    ]
  }
])

const handleChange = (values) => {
  console.log('选中的值:', values)
  // 输出: ['zhujiangxincheng']
}

const handleSelectedItemsChange = (items) => {
  console.log('选中项详细信息:', items)
  // 输出: [{ value: 'zhujiangxincheng', text: '珠江新城街道', path: ['广东省', '广州市', '天河区', '珠江新城街道'] }]
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 选中的值（叶子节点的value数组或路径数组） | Array | [] |
| items | 树形数据 | Array | [] |
| multiple | 是否多选 | Boolean | true |
| levelWidth | 每级的默认宽度（当超过4级时使用） | Number | 120 |
| levelTitles | 级别标题数组 | Array | ['一级', '二级', '三级', '四级', '五级', '六级', '七级', '八级'] |
| selectedItems | 选中项的详细信息（包含text和value） | Array | [] |
| emitPath | 是否返回完整路径（类似element的emitPath） | Boolean | false |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 选中值变化时触发 | (values: Array) |
| change | 选中值变化时触发 | (values: Array) |
| select | 选择某个项目时触发 | (item: Object, levelIndex: Number) |
| update:selectedItems | 选中项详细信息变化时触发 | (items: Array) |

### 数据格式

树形数据需要符合以下格式：

```javascript
[
  {
    text: '显示文本',
    value: '唯一值',
    children: [
      {
        text: '子项显示文本',
        value: '子项唯一值',
        children: [
          // 更多子项...
        ]
      }
    ]
  }
]
```

## 使用场景

### 1. 地区选择
```vue
<multi-level-tree-select
  v-model="selectedAreas"
  :items="areaData"
  :level-titles="['省份', '城市', '区县', '街道']"
  :multiple="true"
/>
```

### 2. 组织架构选择
```vue
<multi-level-tree-select
  v-model="selectedDepts"
  :items="deptData"
  :level-titles="['公司', '部门', '小组', '岗位']"
  :multiple="false"
/>
```

### 3. 商品分类选择
```vue
<multi-level-tree-select
  v-model="selectedCategories"
  :items="categoryData"
  :level-titles="['一级分类', '二级分类', '三级分类']"
  :multiple="true"
/>
```

### 4. 超过3级的复杂结构（支持横向滚动）
```vue
<multi-level-tree-select
  v-model="selectedItems"
  :items="complexData"
  :level-titles="['国家', '省份', '城市', '区县', '街道', '社区']"
  :level-width="150"
  :multiple="true"
/>
```

### 5. 返回完整路径（emitPath模式）
```vue
<template>
  <multi-level-tree-select
    v-model="selectedPaths"
    :items="treeData"
    :emit-path="true"
    :multiple="true"
    @change="handlePathChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedPaths = ref([])

// 当emitPath为true时，返回的数据格式为：
// [
//   ['guangdong', 'guangzhou', 'tianhe', 'zhujiangxincheng'],
//   ['guangdong', 'shenzhen', 'nanshan', 'nantou']
// ]
const handlePathChange = (paths) => {
  console.log('选中的路径:', paths)
  // 每个path都是从根节点到叶子节点的完整路径数组
  paths.forEach((path, index) => {
    console.log(`路径${index + 1}:`, path.join(' -> '))
  })
}
</script>
```

## 样式自定义

组件使用了 CSS 变量，可以通过覆盖这些变量来自定义样式：

```css
.multi-level-tree-select {
  --tree-select-bg: #fff;
  --tree-select-border: #ebedf0;
  --tree-select-text: #323233;
  --tree-select-selected-bg: #e8f4ff;
  --tree-select-selected-text: #1989fa;
}
```

## emitPath 详细说明

### 默认模式（emitPath: false）
```javascript
// 返回选中叶子节点的value数组
selectedValues = ['zhujiangxincheng', 'nantou', 'sanlitun']
```

### 路径模式（emitPath: true）
```javascript
// 返回每个选中节点的完整路径数组
selectedPaths = [
  ['guangdong', 'guangzhou', 'tianhe', 'zhujiangxincheng'],
  ['guangdong', 'shenzhen', 'nanshan', 'nantou'],
  ['beijing', 'beijing-city', 'chaoyang', 'sanlitun']
]
```

### 使用场景对比

**默认模式适用于：**
- 只需要知道最终选中的节点
- 后端API只需要叶子节点ID
- 简单的数据处理场景

**路径模式适用于：**
- 需要知道选中节点的完整层级关系
- 需要显示选中项的完整路径
- 需要根据路径进行复杂的业务逻辑处理
- 类似面包屑导航的需求

## 注意事项

1. 确保每个节点的 `value` 值在整个树中是唯一的
2. 当层级超过3级时，建议设置合适的 `levelWidth` 以获得更好的用户体验
3. 组件会自动处理横向滚动，无需额外配置
4. 支持触摸设备的滑动操作
5. 使用 `emitPath` 时，传入的初始值格式也需要对应调整

## 兼容性

- Vue 3.x
- Vant 4.x
- 现代浏览器（支持 CSS Grid 和 Flexbox）
