<template>
  <div class="multi-level-tree-select">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav" v-if="breadcrumbs.length > 0">
      <div
          class="breadcrumb-item"
          v-for="(crumb, index) in breadcrumbs"
          :key="index"
          @click="navigateToLevel(index)"
          :class="{ 'active': index === currentLevelIndex }"
      >
        <span class="breadcrumb-text">{{ crumb.text }}</span>
        <van-icon
            v-if="index < breadcrumbs.length - 1"
            name="arrow"
            class="breadcrumb-arrow"
        />
      </div>
    </div>

    <!-- 当前级别内容 -->
    <div class="current-level-content">
      <div class="level-items">
        <div
            class="tree-item"
            v-for="item in currentLevelItems"
            :key="item.value"
            :class="{
            'has-children': item.children && item.children.length > 0
          }"
        >
          <div class="item-content">
            <div class="item-main" @click="handleItemClick(item)">
              <span class="item-text">{{ item.text }}</span>
              <van-icon
                  v-if="item.children && item.children.length > 0"
                  name="arrow"
                  class="item-arrow"
              />
            </div>
            <van-checkbox
                :model-value="getCheckboxState(item.value)"
                :indeterminate="isIndeterminate(item.value)"
                @click.stop
                @update:model-value="handleCheckboxChange(item, $event)"
                class="item-checkbox"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed, watch, nextTick} from 'vue'

const props = defineProps({
  // 树形数据
  items: {
    type: Array,
    default: () => []
  },
  // 选中的值
  modelValue: {
    type: Array,
    default: () => []
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 每级的默认宽度（当超过4级时使用）
  levelWidth: {
    type: Number,
    default: 120
  },
  // 选中项的详细信息（包含text和value）
  selectedItems: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'select', 'update:selectedItems'])
const selectedValues = ref([...props.modelValue])
const currentLevelIndex = ref(0)
const navigationPath = ref([])

// 初始化导航路径
const initNavigation = () => {
  navigationPath.value = []
  currentLevelIndex.value = 0
}

// 面包屑数据
const breadcrumbs = computed(() => {
  const crumbs = []
  crumbs.push({text: '全部', value: null, level: 0})

  // 如果没有导航路径，返回空数组
  if (navigationPath.value.length === 0) {
    return crumbs
  }

  // 添加导航路径中的每一级
  navigationPath.value.forEach((item, index) => {
    crumbs.push({
      text: item.text,
      value: item.value,
      level: index + 1
    })
  })

  return crumbs
})

// 当前级别的数据
const currentLevelItems = computed(() => {
  if (navigationPath.value.length === 0) {
    return props.items
  }

  const lastItem = navigationPath.value[navigationPath.value.length - 1]
  return lastItem.children || []
})

// 计算选中项的详细信息（包含text和value）
const selectedItemsWithText = computed(() => {
  const items = []

  // 递归查找所有节点
  const findNodesByValues = (nodes, path = []) => {
    for (const node of nodes) {
      if (selectedValues.value.includes(node.value)) {
        items.push({
          value: node.value,
          text: node.text,
          path: [...path, node.text]
        })
      }
      if (node.children && node.children.length > 0) {
        findNodesByValues(node.children, [...path, node.text])
      }
    }
  }

  findNodesByValues(props.items)
  return items
})


// 导航到指定级别
const navigateToLevel = (levelIndex) => {
  if (levelIndex === 0) {
    // 回到根级别
    navigationPath.value = []
    currentLevelIndex.value = 0
  } else {
    // 回到指定级别（levelIndex对应面包屑中的索引，需要减1来对应navigationPath的索引）
    navigationPath.value = navigationPath.value.slice(0, levelIndex)
    currentLevelIndex.value = levelIndex
  }
}

// 获取所有子节点的值
const getAllChildrenValues = (node) => {
  const values = []
  const traverse = (item) => {
    values.push(item.value)
    if (item.children && item.children.length > 0) {
      item.children.forEach(child => traverse(child))
    }
  }
  traverse(node)
  return values
}

// 获取所有父节点的值
const getAllParentValues = (targetValue, nodes = props.items, parents = []) => {
  for (const node of nodes) {
    const currentPath = [...parents, node.value]
    if (node.value === targetValue) {
      return parents
    }
    if (node.children && node.children.length > 0) {
      const result = getAllParentValues(targetValue, node.children, currentPath)
      if (result) return result
    }
  }
  return null
}

// 获取节点的所有兄弟节点（包括自己）
const getSiblings = (targetValue, nodes = props.items) => {
  const findSiblings = (items, path = []) => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (item.value === targetValue) {
        return items.map(sibling => sibling.value)
      }
      if (item.children && item.children.length > 0) {
        const result = findSiblings(item.children, [...path, item.value])
        if (result) return result
      }
    }
    return null
  }
  return findSiblings(nodes) || []
}

// 检查checkbox状态（简化逻辑，避免递归）
const getCheckboxState = (value) => {
  // 如果直接被选中，返回true
  if (selectedValues.value.includes(value)) {
    return true
  }

  // 检查是否所有叶子节点都被选中
  const node = findNodeByValue(value)
  if (node && node.children && node.children.length > 0) {
    const allLeafValues = getAllChildrenValues(node).slice(1) // 排除自己
    if (allLeafValues.length === 0) return false

    // 检查所有叶子节点是否都在选中列表中
    return allLeafValues.every(leafValue => selectedValues.value.includes(leafValue))
  }

  return false
}

// 检查是否为半选状态（简化逻辑，避免递归）
const isIndeterminate = (value) => {
  const node = findNodeByValue(value)
  if (!node || !node.children || node.children.length === 0) {
    return false
  }

  // 如果当前节点已经完全选中，不显示半选状态
  if (getCheckboxState(value)) {
    return false
  }

  // 获取所有叶子节点
  const allLeafValues = getAllChildrenValues(node).slice(1) // 排除自己
  if (allLeafValues.length === 0) return false

  // 检查有多少叶子节点被选中
  const selectedLeafCount = allLeafValues.filter(leafValue =>
    selectedValues.value.includes(leafValue)
  ).length

  // 如果有部分叶子节点被选中，则显示半选状态
  return selectedLeafCount > 0 && selectedLeafCount < allLeafValues.length
}

// 处理项目点击（只展开，不选中）
const handleItemClick = (item) => {
  // 如果有子级，进入下一级
  if (item.children && item.children.length > 0) {
    navigationPath.value.push(item)
    currentLevelIndex.value = navigationPath.value.length
  }

  // 发出展开事件
  emit('select', item, currentLevelIndex.value)
}

// 处理checkbox变化
const handleCheckboxChange = (item, checked) => {
  if (checked) {
    // 选中：只添加叶子节点（没有子节点的节点）
    addLeafNodes(item)
  } else {
    // 取消选中：移除自己和所有子节点
    const allValues = getAllChildrenValues(item)
    allValues.forEach(value => {
      const index = selectedValues.value.indexOf(value)
      if (index > -1) {
        selectedValues.value.splice(index, 1)
      }
    })

    // 取消父节点选中
    updateParentDeselection(item.value)
  }

  // 发出事件
  emit('update:modelValue', selectedValues.value)
  emit('change', selectedValues.value)
  emit('update:selectedItems', selectedItemsWithText.value)
}

// 添加叶子节点
const addLeafNodes = (node) => {
  if (!node.children || node.children.length === 0) {
    // 这是叶子节点，直接添加
    if (!selectedValues.value.includes(node.value)) {
      selectedValues.value.push(node.value)
    }
  } else {
    // 这是父节点，递归添加所有叶子节点
    node.children.forEach(child => {
      addLeafNodes(child)
    })
  }
}

// 找到节点对象
const findNodeByValue = (value, nodes = props.items) => {
  for (const node of nodes) {
    if (node.value === value) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeByValue(value, node.children)
      if (found) return found
    }
  }
  return null
}


// 更新父节点取消选中状态
const updateParentDeselection = (childValue) => {
  const parents = getAllParentValues(childValue)
  if (!parents || parents.length === 0) return

  // 取消所有父节点的选中状态
  parents.forEach(parentValue => {
    const index = selectedValues.value.indexOf(parentValue)
    if (index > -1) {
      selectedValues.value.splice(index, 1)
    }
  })
}


// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  selectedValues.value = [...newVal]
}, {deep: true})

// 监听items变化
watch(() => props.items, () => {
  initNavigation()
}, {immediate: true, deep: true})

// 监听选中项变化，自动发送详细信息
watch(selectedItemsWithText, (newItems) => {
  emit('update:selectedItems', newItems)
}, {deep: true})
</script>

<style lang="scss" scoped>
.multi-level-tree-select {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .breadcrumb-nav {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #f7f8fa;
    border-bottom: 1px solid #ebedf0;
    overflow-x: auto;
    white-space: nowrap;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    .breadcrumb-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #ff5733;
      }

      &.active {
        color: #ff5733;
        font-weight: 500;
      }

      .breadcrumb-text {
        font-size: 14px;
        color: inherit;
        white-space: nowrap;
      }

      .breadcrumb-arrow {
        margin: 0 8px;
        font-size: 12px;
        color: #969799;
      }
    }
  }

  .current-level-content {
    .level-items {
      max-height: 400px;
      overflow-y: auto;

      .tree-item {
        border-bottom: 1px solid #ebedf0;
        transition: background-color 0.2s;

        &:hover {
          background: #f7f8fa;
        }

        &:last-child {
          border-bottom: none;
        }

        .item-content {
          padding: 12px 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 12px;

          .item-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            padding: 4px 0;

            .item-text {
              flex: 1;
              font-size: 15px;
              color: #323233;
              line-height: 22px;
            }

            .item-arrow {
              color: #969799;
              font-size: 14px;
              margin-left: 8px;
            }
          }

          .item-checkbox {
            flex-shrink: 0;
          }
        }
      }
    }
  }
}
</style>
