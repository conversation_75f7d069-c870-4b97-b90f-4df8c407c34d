<template>
  <van-popup
      :close-on-click-overlay="false"
      :overlay="true"
      teleport="#app"
      :show="show"
      style="width: 100%; height: 100%;"
      class="loading-popup"
      overlay-class="loading-popup"
  >
    <div class="wrapper-pop">
      <van-loading vertical color="white" size="40">
        {{label}}
      </van-loading>
    </div>
  </van-popup>
</template>

<script setup>
defineProps({
  show: {
    type: Boolean,
    required: true
  },
  label: {
    type: String,
    default: '加载中...'
  }
})
</script>

<style scoped lang="scss">
.loading-popup {
  width: 100vw;
  height: 100vh;
  background-color: transparent;

  .wrapper-pop {
    position: fixed;
    left: 50%;
    top: 35%;
    box-sizing: border-box;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    border-radius: 16px;
    padding: 40px 24px 24px;
    width: 220px;
    height: 220px;

    :deep(.van-loading__text) {
      color: #fff;
      margin-top: 12px;
      font-size: 24px;
    }
  }
}
</style>
