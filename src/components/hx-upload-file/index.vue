<script setup>

import {ref, watch} from "vue";
import {axiosPost} from "@/commonjs/axiosUtils";
import {showToast} from "vant";
const emit = defineEmits(['update:modelValue']);
const value = ref([])

const afterRead = async (file) => {
  file.status = 'uploading'
  file.message = '上传中...';
  const formData1 = new FormData();
  formData1.append('file', file.file);
  const {code, result} = await axiosPost('/upload/uploadFile', formData1)
  if (code === 200) {
    file.status = 'done'
    file.data = result
    emit('update:modelValue', value.value)
  }
}

watch(() => value.value, (newValue) => {
  if (!Array.isArray(newValue)) {
    value.value = []
    emit('update:modelValue', value.value)
  } else {
    value.value = newValue || []
    emit('update:modelValue', value.value)
  }
})

const props = defineProps({
  modelValue: {
    type: [Array],
    default: null
  },
  maxCount: {
    type: Number,
    default: 9
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  text: {
    type: String,
    default: '上传佐证材料(照片）'
  }
})

const beforeRead = (file) => {
  if (file.type.match(/heic|heif/i)) {
    showToast('该文件格式暂不兼容，请转换后重新上传')
    return false
  }
  return true
}

watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.length > 0) {
    value.value = newValue || []
  } else {
    if (value.value.length !== 0) {
      value.value = []
    }
  }
})

</script>

<template>
  <van-field
      :rules="[{ required: required, message: placeholder }]"
      name="uploader"
      style="flex: none">
    <template #input>
      <van-uploader
          ref="upload"
          v-model="value"
          :after-read="(file)=>afterRead(file,value)"
          :before-read="beforeRead"
          :disabled="disabled"
          :max-count="maxCount"
          accept=".jpg,.jpeg,.png"
          class="upload"
          style="margin: 20px 0 10px;width: 100%;"
          upload-text="选择图片">
        <div class="add-wrapper">
          <div class="icon-add">
            <van-icon color="black" name="plus" size="18" style="margin-right: 3px"/>
            {{ text }}
          </div>
        </div>
      </van-uploader>
    </template>
  </van-field>
</template>

<style lang="scss" scoped>
.add-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;

  .icon-add {
    display: flex;
    color: var(--theme-color);
    align-items: center;
    font-size: 28px;
    width: 590px;
    height: 80px;
    opacity: 1;
    border-radius: 200px;
    background: rgba(99, 180, 255, 0.1);
    justify-content: center;
  }
}


:deep(.van-uploader__input-wrapper) {
  width: 100%;
}

:deep(.van-image__img) {
}

.edit-text {
  display: flex;
  font-size: 24px;
  color: white;
  height: 100%;
  padding-bottom: 6px;
  align-items: flex-end;
  justify-content: center;
}
</style>