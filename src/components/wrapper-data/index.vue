<script setup>
import TitleWrapper from "@/components/titleWrapper.vue";
defineProps({
  showBack: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ""
  }
});
</script>

<template>
  <div class="wrapper-home">
    <title-wrapper class="title-wrapper"
                   v-if="false"
                   :show-back="showBack" :title="title"></title-wrapper>
    <div class="data-wrapper-data">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.wrapper-home {
  width: 100%;
  height: 100%;

  .title-wrapper {
    width: 100%;
    background-color: #eeeeee;
    height: 88px;
  }

  .data-wrapper-data {
    width: 100%;
    overflow: hidden;
    display: block;
    height: calc(100%);
    max-height: calc(100%);
  }
}
</style>