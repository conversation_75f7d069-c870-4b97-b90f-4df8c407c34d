<script setup>
import {ref, watch} from "vue";
import {axiosPost} from "@/commonjs/axiosUtils";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  maxCount: {
    type: Number,
    default: 1
  },
  showAdd: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['update:modelValue']);
const afterRead = async (file) => {
  const formData1 = new FormData();
  file.status = 'uploading'
  formData1.append('file', file.file);
  const {code, result} = await axiosPost('/upload/uploadFile', formData1)
  if (code === 200) {
    file.status = 'done'
    emit('update:modelValue', result.materialUrl)
  }
}

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    value.value = [
      {
        url: newValue,
        status: 'done'
      }
    ]
  } else {
    value.value = []
  }
})


const value = ref([])


</script>

<template>
  <van-uploader
      ref="upload"
      v-model="value"
      :after-read="afterRead"
      :deletable="false"
      :max-count="maxCount"
      accept=".png,.jpg,.jpeg"
      class="upload"
      reupload
      upload-text="选择图片">

    <div class="icon-add">
      <van-icon color="#cccccc" name="user-circle-o" size="80"/>
      <div v-if="showAdd" class="text">上传头像</div>
    </div>

    <template #preview-cover="{ file }">
      <div class="edit-text">编辑</div>
    </template>
  </van-uploader>
</template>

<style lang="scss" scoped>
.icon-add {
  display: flex;
  color: var(--theme-color);
  flex-direction: column;
  align-items: center;
  font-size: 20px;
}

:deep(.van-image__img) {
  border-radius: 80px;
}

.edit-text {
  display: flex;
  font-size: 24px;
  color: white;
  height: 100%;
  padding-bottom: 6px;
  align-items: flex-end;
  justify-content: center;
}
</style>