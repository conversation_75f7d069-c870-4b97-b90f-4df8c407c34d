import axios from 'axios'
import qs from 'qs'
import {getToken, getUserInfo, resetData} from "@/commonjs/accountUtils";
import {showToast} from "vant";
import router from "@/router";

const errorCode = {
    'default': '系统异常，请稍后再试',
    '403': '没有权限',
    '404': '请求地址不存在',
    '405': '请求方法错误',
    '500': '服务器内部错误',
    '501': '服务未实现',
    '502': '网关错误',
    '503': '服务不可用',
    '504': '网关超时',
    '505': 'HTTP版本不受支持',
}

let _axios = axios.create({
    timeout: 600000,
    baseURL: import.meta.env.VITE_APP_BASE_API,
    validateStatus: function (status) {
        return status >= 200
    }
})

_axios.interceptors.request.use(config => {
    if (config.method === 'post' && config.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
        config.transformRequest = [function (data) {
            return qs.stringify(data)
        }]
    }
    let token = getToken()
    let userInfo = getUserInfo()
    config.headers['Authorization'] = token;
    config.headers['unitid'] = userInfo.userInfo;
    return config
}, function (error) {
    return Promise.reject(error)
})

_axios.interceptors.response.use(res => {
    if (res.status !== 200) {
        showToast({
            type: 'fail',
            message: '网络异常'
        })
        return Promise.resolve({})
    }

    const code = res.data.code || 200;
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    const requestShowMsg = res.config.custom?.showMsg

    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
        return res
    }
    if (code === 1001 || code === 401 || code === 403) {
        router.replace('/login')
        return Promise.reject('error')
    } else if (code !== 200 && msg && requestShowMsg) {
        showToast({
            type: 'fail',
            message: res.data.msg
        })
        return Promise.resolve(res.data)
    }
    return Promise.resolve(res.data)
}, error => {
    let {message} = error;
    if (message === "Network Error") {
        message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
        message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
        message = "系统接口" + message.substr(message.length - 3) + "异常";
    }
    if (message) {
        showToast({
            type: 'fail',
            message: message
        })
    }
    return Promise.reject(error)
})

export default _axios
