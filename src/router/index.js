import {createRouter, createWebHistory} from 'vue-router'
import store from "../store";
import {getToken} from "@/commonjs/accountUtils";

const routes = [
    {
        path: '/',
        redirect: '/home'
    },
    {
        path: '/home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
            keepAlive: true,
            title: '无感课堂点名'
        }
    },
    {
        path: '/editPassword',
        name: 'editPassword',
        component: () => import('@/views/home/<USER>'),
        meta: {
            title: '无感课堂点名'
        }
    },
    {
        path: '/detail',
        name: 'detail',
        component: () => import('@/views/home/<USER>'),
        meta: {
            title: '无感课堂点名'
        }
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login/index.vue'),
        meta: {
            title: '无感课堂点名'
        }
    },

]

const router = createRouter({
    history: createWebHistory(import.meta.env.VITE_APP_BASE_FILE),
    routes
})

router.beforeEach((to, from, next) => {
    if (to.meta.title) {
        document.title = to.meta.title
    }

    if (to.meta.keepAlive) {
        let keepAlive = store.state.keepAlive || []
        const componentName = to.name
        if (!keepAlive.includes(componentName)) {
            store.commit('ADD_KEEP_ALIVE', componentName)
        }
    }
    if (!getToken() && to.name !== 'login') {
        next({
            path: '/login',
            query: {
                redirect: encodeURIComponent(to.fullPath)
            },
            replace: true
        })
        return
    }
    next()
})

export default router
