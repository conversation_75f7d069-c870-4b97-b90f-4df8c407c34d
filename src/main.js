
import {createApp} from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import VConsole from "vconsole";
import * as vant from "vant";
import {getVal} from "@/commonjs/paramsUtils.js";
import 'vant/lib/index.css';
import WrapperData from '@/components/wrapper-data/index.vue'
import LoadingPop from "@/components/loading-pop.vue";
import InputItem from "@/components/input-item/index.vue";
import HxUpload from "@/components/hx-upload/index.vue";
import HxUploadFile from "@/components/hx-upload-file/index.vue";
import WeekSlider from "@/components/weekSlider/index.vue";
import {setCorpId} from "@/commonjs/accountUtils";
import './assets/base.css'

if (process.env.NODE_ENV === 'development' || Number(getVal('vconsoleShow')) === 1) {
    new VConsole()
}

const corpId = getVal('corpId')
if (corpId) setCorpId(corpId)

const app = createApp(App);
app.component('HxUpload', HxUpload)
app.component('WeekSlider', WeekSlider)
app.component('HxUploadFile', HxUploadFile)
app.component('InputItem', InputItem)
app.component('WrapperData', WrapperData)
app.component('LoadingPop', LoadingPop)
app.use(router)
    .use(store)
    .use(vant)
    .use(vant.Lazyload)
app.mount('#app')
