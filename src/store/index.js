import {createStore} from 'vuex'

export default createStore({
    state: {
        keepAlive: [],
        currentRole: null,
        userInfo: {}
    },
    getters: {},
    mutations: {
        SET_USER_INFO(state, data) {
            state.userInfo = data
        },
        SET_CURRENT_ROLE(state, data) {
            state.currentRole = data
        },
        CLEAR_USER_INFO(state) {
            state.userInfo = {}
        },
        ADD_KEEP_ALIVE(state, name) {
            if (!state.keepAlive.includes(name)) {
                state.keepAlive = [...state.keepAlive, name]
            }
        },
        REMOVE_KEEP_ALIVE(state, name) {
            state.keepAlive = state.keepAlive.filter(n => n !== name)
        }
    },
    actions: {},
    modules: {}
})
