/**
 * Vue 3 组合式API - 事件总线
 * 提供在组件中使用事件总线的便捷方法
 */

import { onUnmounted } from 'vue'
import eventBus from '@/utils/eventBus'

/**
 * 使用事件总线的组合式API
 * @returns {object} - 事件总线相关方法
 */
export function useEventBus() {
  // 存储当前组件的事件监听器，用于组件卸载时自动清理
  const listeners = []

  /**
   * 监听事件（组件卸载时自动清理）
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   * @returns {function} - 手动取消监听的函数
   */
  const on = (eventName, callback) => {
    const unsubscribe = eventBus.on(eventName, callback)
    listeners.push(unsubscribe)
    return unsubscribe
  }

  /**
   * 监听一次事件（触发后自动移除）
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   * @returns {function} - 手动取消监听的函数
   */
  const once = (eventName, callback) => {
    const unsubscribe = eventBus.once(eventName, callback)
    listeners.push(unsubscribe)
    return unsubscribe
  }

  /**
   * 发送事件
   * @param {string} eventName - 事件名称
   * @param {...any} args - 传递给回调函数的参数
   */
  const emit = (eventName, ...args) => {
    eventBus.emit(eventName, ...args)
  }

  /**
   * 取消监听事件
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   */
  const off = (eventName, callback) => {
    eventBus.off(eventName, callback)
  }

  // 组件卸载时自动清理所有监听器
  onUnmounted(() => {
    listeners.forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribe()
      }
    })
    listeners.length = 0
  })

  return {
    on,
    once,
    emit,
    off,
    eventBus
  }
}

/**
 * 专门用于数据刷新的组合式API
 * @returns {object} - 数据刷新相关方法
 */
export function useDataRefresh() {
  const { on, emit } = useEventBus()
  return {
    on,
    emit
  }
}

export default useEventBus
