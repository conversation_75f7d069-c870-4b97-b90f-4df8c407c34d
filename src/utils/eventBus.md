# 全局事件总线使用指南

## 概述

全局事件总线是一个基于 Vue 3 的轻量级事件系统，用于在不同组件之间进行通信。特别适用于跨页面的数据同步和状态更新。

## 核心文件

- `src/utils/eventBus.js` - 事件总线核心实现
- `src/composables/useEventBus.js` - Vue 3 组合式API封装

## 基础用法

### 1. 在组件中导入

```javascript
import { useEventBus, useDataRefresh } from '@/composables/useEventBus'
```

### 2. 基础事件操作

```javascript
<script setup>
import { useEventBus } from '@/composables/useEventBus'

const { on, emit, EVENT_NAMES } = useEventBus()

// 监听事件
on('custom-event', (data) => {
  console.log('收到事件:', data)
})

// 发送事件
const sendEvent = () => {
  emit('custom-event', { message: 'Hello World' })
}
</script>
```

### 3. 数据刷新专用API

```javascript
<script setup>
import { useDataRefresh } from '@/composables/useEventBus'

const { 
  onHomeListRefresh, 
  refreshHomeList,
  onAttendanceUpdate,
  updateAttendance 
} = useDataRefresh()

// 监听首页列表刷新
onHomeListRefresh((data) => {
  console.log('首页需要刷新:', data)
  // 执行刷新逻辑
  loadData()
})

// 触发首页列表刷新
const triggerRefresh = () => {
  refreshHomeList({ 
    source: 'detail-page',
    reason: 'data-updated' 
  })
}
</script>
```

## 预定义事件

### 数据刷新相关
- `REFRESH_HOME_LIST` - 刷新首页列表
- `REFRESH_CLASS_DATA` - 刷新班级数据
- `REFRESH_ATTENDANCE_DATA` - 刷新考勤数据

### 数据变更相关
- `DATA_CHANGED` - 通用数据变更
- `ATTENDANCE_UPDATED` - 考勤数据更新
- `CLASS_INFO_UPDATED` - 班级信息更新

### 用户操作相关
- `USER_LOGIN` - 用户登录
- `USER_LOGOUT` - 用户登出
- `USER_INFO_UPDATE` - 用户信息更新

## 实际应用场景

### 场景1：详情页修改数据后刷新列表页

**详情页 (detail.vue):**
```javascript
<script setup>
import { useDataRefresh } from '@/composables/useEventBus'

const { refreshHomeList, updateAttendance } = useDataRefresh()

// 保存考勤数据
const saveAttendance = async (data) => {
  try {
    await api.saveAttendance(data)
    
    // 通知首页刷新
    refreshHomeList({
      source: 'attendance-update',
      timestamp: Date.now()
    })
    
    // 或者发送具体的考勤更新事件
    updateAttendance(data)
    
    showToast('保存成功')
    router.back()
  } catch (error) {
    showToast('保存失败')
  }
}
</script>
```

**列表页 (home.vue):**
```javascript
<script setup>
import { useDataRefresh } from '@/composables/useEventBus'

const { onHomeListRefresh, onAttendanceUpdate } = useDataRefresh()

// 监听刷新事件
onHomeListRefresh((data) => {
  console.log('收到刷新请求:', data)
  refreshList()
})

// 监听考勤更新事件
onAttendanceUpdate((data) => {
  console.log('考勤数据更新:', data)
  // 可以进行更精细的更新操作
  updateSpecificItem(data)
})

const refreshList = () => {
  // 刷新列表逻辑
  loadData()
}
</script>
```

### 场景2：用户登录状态变更

**登录组件:**
```javascript
<script setup>
import { useEventBus } from '@/composables/useEventBus'

const { emit, EVENT_NAMES } = useEventBus()

const login = async (credentials) => {
  try {
    const userInfo = await api.login(credentials)
    
    // 发送登录成功事件
    emit(EVENT_NAMES.USER_LOGIN, userInfo)
    
    router.push('/home')
  } catch (error) {
    showToast('登录失败')
  }
}
</script>
```

**其他组件监听:**
```javascript
<script setup>
import { useEventBus } from '@/composables/useEventBus'

const { on, EVENT_NAMES } = useEventBus()

// 监听用户登录
on(EVENT_NAMES.USER_LOGIN, (userInfo) => {
  console.log('用户已登录:', userInfo)
  // 更新用户相关的UI状态
  updateUserUI(userInfo)
})
</script>
```

## 最佳实践

### 1. 事件命名规范
- 使用预定义的 `EVENT_NAMES` 常量
- 自定义事件使用小写字母和连字符：`custom-event`
- 避免使用过于通用的事件名

### 2. 数据传递
```javascript
// ✅ 推荐：传递结构化数据
emit('data-update', {
  type: 'attendance',
  id: '123',
  action: 'create',
  timestamp: Date.now(),
  data: { ... }
})

// ❌ 避免：传递过多参数
emit('data-update', id, type, action, data, timestamp)
```

### 3. 错误处理
```javascript
// 在事件处理函数中添加错误处理
on('data-update', (data) => {
  try {
    handleDataUpdate(data)
  } catch (error) {
    console.error('处理数据更新失败:', error)
    showToast('数据更新失败')
  }
})
```

### 4. 内存管理
- 使用 `useEventBus()` 组合式API会自动清理事件监听器
- 手动监听时记得在组件卸载时清理：

```javascript
import { onUnmounted } from 'vue'
import eventBus from '@/utils/eventBus'

const unsubscribe = eventBus.on('custom-event', handler)

onUnmounted(() => {
  unsubscribe()
})
```

## 调试技巧

### 1. 事件日志
```javascript
// 在开发环境中启用事件日志
if (import.meta.env.DEV) {
  const originalEmit = eventBus.emit
  eventBus.emit = function(eventName, ...args) {
    console.log(`[EventBus] 发送事件: ${eventName}`, args)
    return originalEmit.call(this, eventName, ...args)
  }
}
```

### 2. 查看活跃监听器
```javascript
// 查看当前所有事件
console.log('活跃事件:', eventBus.eventNames())

// 查看特定事件的监听器数量
console.log('监听器数量:', eventBus.listenerCount('refresh-home-list'))
```

## 注意事项

1. **避免循环事件**：确保事件处理不会触发相同的事件
2. **性能考虑**：避免在高频事件中进行重量级操作
3. **类型安全**：在 TypeScript 项目中定义事件类型
4. **测试友好**：在单元测试中可以监听事件来验证行为

## 示例页面

- `/detail-example` - 详情页示例，演示如何发送刷新事件
- `/emit-path-test` - EmitPath功能测试页面

访问这些页面可以看到事件总线的实际使用效果。
