/**
 * 全局事件总线
 * 基于 Vue 3 的事件系统实现
 */

class EventBus {
  constructor() {
    // 存储所有事件监听器
    this.events = new Map()
  }

  /**
   * 监听事件
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   * @returns {function} - 取消监听的函数
   */
  on(eventName, callback) {
    if (!this.events.has(eventName)) {
      this.events.set(eventName, new Set())
    }
    
    this.events.get(eventName).add(callback)
    
    // 返回取消监听的函数
    return () => {
      this.off(eventName, callback)
    }
  }

  /**
   * 监听一次事件（触发后自动移除）
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   * @returns {function} - 取消监听的函数
   */
  once(eventName, callback) {
    const onceCallback = (...args) => {
      callback(...args)
      this.off(eventName, onceCallback)
    }
    
    return this.on(eventName, onceCallback)
  }

  /**
   * 取消监听事件
   * @param {string} eventName - 事件名称
   * @param {function} callback - 回调函数
   */
  off(eventName, callback) {
    if (this.events.has(eventName)) {
      this.events.get(eventName).delete(callback)
      
      // 如果没有监听器了，删除事件
      if (this.events.get(eventName).size === 0) {
        this.events.delete(eventName)
      }
    }
  }

  /**
   * 发送事件
   * @param {string} eventName - 事件名称
   * @param {...any} args - 传递给回调函数的参数
   */
  emit(eventName, ...args) {
    if (this.events.has(eventName)) {
      // 复制一份监听器列表，避免在执行过程中被修改
      const callbacks = Array.from(this.events.get(eventName))
      
      callbacks.forEach(callback => {
        try {
          callback(...args)
        } catch (error) {
          console.error(`EventBus: Error in event "${eventName}" callback:`, error)
        }
      })
    }
  }

  /**
   * 移除所有监听器
   * @param {string} eventName - 事件名称（可选，不传则清除所有事件）
   */
  clear(eventName) {
    if (eventName) {
      this.events.delete(eventName)
    } else {
      this.events.clear()
    }
  }

  /**
   * 获取事件的监听器数量
   * @param {string} eventName - 事件名称
   * @returns {number} - 监听器数量
   */
  listenerCount(eventName) {
    return this.events.has(eventName) ? this.events.get(eventName).size : 0
  }

  /**
   * 获取所有事件名称
   * @returns {string[]} - 事件名称数组
   */
  eventNames() {
    return Array.from(this.events.keys())
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus()


export default eventBus

// 导出便捷方法
export const { on, once, off, emit, clear } = eventBus
