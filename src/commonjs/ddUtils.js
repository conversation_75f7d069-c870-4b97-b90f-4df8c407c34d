import * as dd from "dingtalk-jsapi";
import {getAppInfo, getCorpId} from "@/commonjs/accountUtils";
import {axiosGet} from "@/commonjs/axiosUtils";

export function ddSelectUser(options = {
    multiple: true,
    responseUserOnly: true,//只返回人
    maxSelect: 1000,
    selectUserList: [],
    disableUserList: [],
    selectDeptList: [],
    disableDeptList: [],
    requiredUsers: [],
    requiredDepartments: [],
}) {
    const params = {
        title: '选择授权用户', //标题
        corpId: getCorpId(), //企业的corpId
        multiple: options.multiple, //是否多选
        limitTips: '超出了最大可选人数', //超过限定人数返回提示
        maxUsers: options.maxSelect, //最大可选人数
        pickedUsers: options.selectUserList,
        pickedDepartments: options.selectDeptList, //已选部门
        disabledUsers: options.disableUserList, //不可选用户
        disabledDepartments: options.disableDeptList, //不可选部门
        requiredUsers: options.requiredUsers, //必选用户（不可取消选中状态）
        requiredDepartments: options.requiredDepartments, //必选部门（不可取消选中状态）
        appId: getAppInfo().agentId, //微应用Id，企业内部应用查看AgentId
        permissionType: 'GLOBAL', //可添加权限校验，选人权限，目前只有GLOBAL这个参数
        responseUserOnly: options.responseUserOnly, //返回人，或者返回人和部门
        startWithDepartmentId: 0, //仅支持0和-1
    }
    return new Promise((resolve, reject) => {
        dd.ready(() => {
            dd.biz.contact.complexPicker({
                ...params,
                success: (res) => {
                    resolve(res)
                },
                fail: (err) => {
                    reject(err)
                    console.log('ddSign--fail', err)
                },
            })
        })
    })
}

export function isDdPlatform() {
    return dd.env.platform !== 'notInDingTalk'
}

export async function signDdFun() {
    if (dd.env.platform === 'notInDingTalk') {
        return
    }

    let url = window.location.href.split('#')[0]
    let params = {
        corpId: getCorpId(),
        nonceStr: 'student-sign',
        timeStamp: new Date().getTime(),
        url: url,
    }
    let {code, timestamp, result} = await axiosGet('/pc/manage/role/getSign', params)
    if (code === 200) {
        let data = await ddSignUtils({
            timeStamp: params.timeStamp,
            nonceStr: params.nonceStr,
            result: result
        })
    }
}

export function ddSignUtils(options) {
    const params = {
        agentId: getAppInfo().agentId,
        corpId: getCorpId(),
        timeStamp: options.timeStamp,
        nonceStr: options.nonceStr,
        signature: options.result,
        type: 0,
    }
    return new Promise((resolve, reject) => {
        dd.config({
            ...params,
            jsApiList: [
                'runtime.info',
                'biz.contact.choose',
                'device.notification.confirm',
                'device.notification.alert',
                'device.notification.prompt',
                'biz.ding.post',
                'biz.util.openLink',
                'biz.contact.complexPicker'
            ],
        })
        dd.error(err => {
            console.log('----------===', err)
            reject(err)
        })
        resolve()
    })

}
