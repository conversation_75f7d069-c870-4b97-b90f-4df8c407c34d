import axios from '../axios'

/**
 * get请求
 * @param url 请求路径
 * @param params 参数
 * @param showMsg 参数
 * */
async function axiosGet(url, params = undefined, showMsg = true) {
    return await axios.get(url, {
        params: params,
        custom: {
            showMsg: showMsg
        }
    })
}


/**
 * post请求
 * @param url 请求路径
 * @param data 参数
 * @param params 参数
 * @param showMsg 参数
 * @param useForm 是否使用form表单，默认使用json表单
 */
async function axiosPost(url, data = undefined, params, showMsg = true, useForm = false,) {
    return await axios.post(url, data,
        {
            headers: !useForm ? undefined : {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            params,
            custom: {
                showMsg: showMsg
            }
        })
}

async function axiosUploadFile(url, params = undefined, showMsg = true) {
    return await axios.post(url, params, {
        headers: {
            'content-type': 'multipart/form-data',
        },
        custom: {
            showMsg: showMsg
        }
    })
}

/**
 * delete请求
 * @param url
 * @param params
 * @param showMsg
 */
async function axiosDelete(url, params = undefined, showMsg = true) {
    return await axios.delete(url, {
        params: params,
        custom: {
            showMsg: showMsg
        },

    })
}

export {
    axiosUploadFile,
    axiosGet,
    axiosPost,
    axiosDelete
}
