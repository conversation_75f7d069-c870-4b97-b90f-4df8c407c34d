import router from "@/router";

function setUserInfo(data) {
    sessionStorage.setItem('userInfo-data', JSON.stringify(data) || '')
}

function getUserInfo() {
    const userInfoStr = sessionStorage.getItem('userInfo-data') || undefined;
    let userInfo = {}
    if (!userInfoStr) {
        return userInfo
    }
    try {
        userInfo = JSON.parse(userInfoStr)
    } catch (e) {
        userInfo = {}
        console.log(e)
    }
    return userInfo
}

function setAppInfo(data) {
    sessionStorage.setItem('appInfo-data', JSON.stringify(data) || '')
}

function getAppInfo() {
    const userInfoStr = sessionStorage.getItem('appInfo-data') || undefined;
    let userInfo = {}
    if (!userInfoStr) {
        return userInfo
    }
    try {
        userInfo = JSON.parse(userInfoStr)
    } catch (e) {
        userInfo = {}
        console.log(e)
    }
    return userInfo
}


function getToken() {
    return sessionStorage.getItem('app-token') || undefined
}

function setToken(token) {
    sessionStorage.setItem('app-token', token || '')
}

function getCorpId() {
    return sessionStorage.getItem('app-corpId') || undefined
}

function setCorpId(corpId) {
    sessionStorage.setItem('app-corpId', corpId || '')
}

function resetData() {
    setToken('')
    setUserInfo('')
    setAppInfo('')
}

export {
    getAppInfo,
    setAppInfo,
    getCorpId,
    setCorpId,
    resetData,
    setUserInfo,
    setToken,
    getUserInfo,
    getToken
}
