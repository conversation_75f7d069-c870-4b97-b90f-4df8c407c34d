<script setup>
import {computed} from 'vue'
import {useStore} from 'vuex'
const store = useStore()
const keepAliveList = computed(() => store.state.keepAlive || [])

const updateHeight = () => {
  document.documentElement.style.setProperty('--window-height', `${window.innerHeight}px`)
}
window.addEventListener('resize', updateHeight)
updateHeight()
</script>
<template>
  <van-config-provider
      style="width: 100%;height: 100%;overflow: hidden"
      theme-vars-scope="global">
    <router-view v-slot="{ Component }">
      <keep-alive :include="keepAliveList">
        <component :is="Component"/>
      </keep-alive>
    </router-view>
  </van-config-provider>
  <van-number-keyboard safe-area-inset-bottom />
</template>
<style scoped>

</style>
